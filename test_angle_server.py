#!/usr/bin/env python3
"""
测试用的角度数据WebSocket服务器
模拟6个关节的角度数据，用于测试Web仿真环境的实时显示功能
"""

import asyncio
import json
import time
import math
import websockets
from websockets.server import serve

class TestAngleServer:
    def __init__(self, port=8765):
        self.port = port
        self.clients = set()
        self.is_running = False
        
        # 模拟的关节角度数据
        self.joint_angles = {
            1: 0.0,  # 腰部旋转
            2: 0.0,  # 大臂控制
            3: 0.0,  # 小臂控制
            4: 0.0,  # 腕部控制
            5: 0.0,  # 腕部旋转
            6: 0.0   # 爪子控制
        }
        
        # 模拟参数
        self.time_start = time.time()
        
    async def handle_client(self, websocket, path):
        """处理WebSocket客户端连接"""
        print(f"新的客户端连接: {websocket.remote_address}")
        self.clients.add(websocket)
        
        try:
            # 发送当前角度数据
            await self.send_angles(websocket)
            # 保持连接
            await websocket.wait_closed()
        except websockets.exceptions.ConnectionClosed:
            print(f"客户端断开连接: {websocket.remote_address}")
        finally:
            self.clients.discard(websocket)
    
    async def send_angles(self, websocket=None):
        """发送角度数据"""
        data = {
            "type": "joint_angles",
            "timestamp": time.time(),
            "angles": self.joint_angles.copy()
        }
        message = json.dumps(data)
        
        if websocket:
            # 发送给特定客户端
            try:
                await websocket.send(message)
            except websockets.exceptions.ConnectionClosed:
                pass
        else:
            # 广播给所有客户端
            if self.clients:
                disconnected = set()
                for client in self.clients:
                    try:
                        await client.send(message)
                    except websockets.exceptions.ConnectionClosed:
                        disconnected.add(client)
                
                # 移除断开的客户端
                self.clients -= disconnected
    
    def update_angles(self):
        """更新模拟的角度数据"""
        current_time = time.time() - self.time_start
        
        # 使用正弦波模拟不同关节的运动
        self.joint_angles[1] = 30 * math.sin(current_time * 0.5)  # 腰部旋转，慢速
        self.joint_angles[2] = 45 * math.sin(current_time * 0.8)  # 大臂控制
        self.joint_angles[3] = 60 * math.sin(current_time * 1.0)  # 小臂控制
        self.joint_angles[4] = 30 * math.sin(current_time * 1.2)  # 腕部控制
        self.joint_angles[5] = 90 * math.sin(current_time * 0.6)  # 腕部旋转
        self.joint_angles[6] = 15 * math.sin(current_time * 2.0)  # 爪子控制，快速
    
    async def broadcast_loop(self):
        """定期广播角度数据"""
        while self.is_running:
            # 更新角度
            self.update_angles()
            
            # 广播给所有客户端
            await self.send_angles()
            
            # 打印当前角度（可选）
            print(f"广播角度数据: {[f'{k}:{v:.1f}°' for k, v in self.joint_angles.items()]}")
            
            # 等待100ms（10Hz更新频率）
            await asyncio.sleep(0.1)
    
    async def start_server(self):
        """启动WebSocket服务器"""
        print(f"启动测试角度服务器在端口 {self.port}")
        self.is_running = True
        
        # 启动WebSocket服务器
        server = await serve(self.handle_client, "localhost", self.port)
        print(f"WebSocket服务器运行在: ws://localhost:{self.port}")
        
        # 启动广播循环
        broadcast_task = asyncio.create_task(self.broadcast_loop())
        
        try:
            # 等待服务器关闭
            await server.wait_closed()
        except KeyboardInterrupt:
            print("\n收到停止信号")
        finally:
            self.is_running = False
            broadcast_task.cancel()
            server.close()
            await server.wait_closed()

async def main():
    """主函数"""
    server = TestAngleServer(port=8765)
    
    try:
        await server.start_server()
    except KeyboardInterrupt:
        print("\n测试服务器已停止")

if __name__ == "__main__":
    print("启动测试角度数据服务器...")
    print("这个服务器会模拟6个关节的角度数据，用于测试Web仿真环境")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已停止")
