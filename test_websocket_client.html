<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebSocket Test Client</h1>
    <div id="status" class="status disconnected">Disconnected</div>
    <button id="connectBtn">Connect</button>
    <button id="disconnectBtn" disabled>Disconnect</button>
    <h3>Log:</h3>
    <div id="log" class="log"></div>

    <script>
        let websocket = null;
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function connect() {
            try {
                log('Attempting to connect to ws://localhost:8765...');
                websocket = new WebSocket('ws://localhost:8765');

                websocket.onopen = function(event) {
                    log('Connected to WebSocket server');
                    updateStatus(true);
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`Received: ${JSON.stringify(data, null, 2)}`);
                    } catch (error) {
                        log(`Received (raw): ${event.data}`);
                    }
                };

                websocket.onclose = function(event) {
                    log(`Connection closed (code: ${event.code}, reason: ${event.reason})`);
                    updateStatus(false);
                    websocket = null;
                };

                websocket.onerror = function(error) {
                    log(`WebSocket error: ${error}`);
                    updateStatus(false);
                };

            } catch (error) {
                log(`Error creating WebSocket: ${error}`);
                updateStatus(false);
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
            }
        }

        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);

        // Auto-connect on page load
        connect();
    </script>
</body>
</html>
